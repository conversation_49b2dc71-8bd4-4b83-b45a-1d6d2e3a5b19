# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

DROID Tools is a command-line toolkit for DROID synthesizer devices. It features a hybrid architecture combining Go for the CLI interface with a C library for Core MIDI integration on macOS.

## Memories
- Always prompt me to connect or disconnect the hardware unit DROID X7 MIDI when required during testing.

## Build Commands

### Primary Build Commands
```bash
make                 # Build the main droid CLI tool
make test            # Run all tests (unit + smoke)
make test-unit       # Run unit tests only
make test-smoke      # Run smoke tests only
make fmt             # Format Go code
make install         # Install to ~/bin
make clean           # Clean build artifacts
```

### DROID-Specific Upload Commands
```bash
make configs/filename.ini                    # Upload config file directly
make upload CONFIG=file.ini                  # Upload with CONFIG variable
make dry-upload CONFIG=file.ini              # Test upload without sending
make wipe                                    # Wipe DROID configuration
make list-devices                            # List MIDI devices
```

## Architecture

### Hybrid Architecture Design
The codebase uses a unique hybrid approach:
- **Go CLI Application** (`cmd/droid/`): User interface, commands, business logic
- **C Library Backend** (`lib/`): Core MIDI integration and DROID protocol handling
- **Go-C Bridge** (`pkg/droid/droid.go`): CGo bindings connecting Go and C layers

### Key Components

#### CLI Layer (`cmd/droid/`)
- `main.go`: Entry point, uses Cobra command framework
- `commands/`: All CLI command implementations
  - `upload.go`: Configuration upload with SysEx protocol and device connectivity detection
  - `device.go`: MIDI device listing and management
  - `circuit.go`: Circuit library search and documentation
  - `validate.go`: Configuration file validation
  - `config.go`: Template and configuration management

#### Go Packages (`pkg/`)
- `droid/`: Core functionality and C library bindings
  - `droid.go`: Main CGo bridge with C library
  - `parser.go`: DROID INI configuration parser
  - `errors.go`: Structured error types
- `circuits/`: Circuit registry and embedded database
  - `registry.go`: Circuit definitions and search
  - `types.go`: Circuit parameter types and validation
  - `embedded/droid-firmware.json`: Official DROID firmware data
- `display/`: CLI output formatting and tables
- `logger/`: Structured logging system using logrus
  - `logger.go`: Global logger with custom formatters and level control

#### C Library (`lib/`)
- `libdroid.c/h`: Main library interface and device enumeration
- `midi.c/h`: Core MIDI client management and communication
- `sysex.c/h`: SysEx message encoding and DROID protocol implementation
- `file_utils.c/h`: File I/O operations with error handling

### DROID SysEx Protocol
The C library implements the DROID-specific SysEx protocol:
- Header: `F0 00 66 66 50` (SysEx start + DROID manufacturer ID)
- Padding: Space bytes (0x20) inserted after every 255 bytes including header
- Supports empty file uploads to wipe DROID configurations

## Development Workflow

### Testing Strategy
- **Unit Tests**: Go packages in `pkg/` have comprehensive test coverage
- **Smoke Tests**: CLI commands tested for basic functionality
- **Command Tests**: CLI commands in `cmd/droid/commands/` have specific tests

### Code Structure Patterns
- **Error Handling**: Structured error types in `pkg/droid/errors.go`
- **CGo Integration**: All C library calls go through `pkg/droid/droid.go`
- **Command Pattern**: Each CLI command is a separate file in `commands/`
- **Registry Pattern**: Circuits stored in embedded registry with search capabilities

### Build Dependencies
- **Go 1.24+**: Main language
- **Clang**: For C library compilation
- **CoreMIDI Framework**: macOS-specific MIDI support
- **CoreFoundation Framework**: macOS system integration

### Important Files
- `go.mod`: Uses Cobra CLI framework, go-pretty for tables, logrus for logging
- `Makefile`: Unified build system handling both Go and C compilation
- `lib/Makefile`: Separate C library build with static linking
- `pkg/circuits/embedded/droid-firmware.json`: Official circuit database

## Key Concepts

### Circuit Library System
- Circuits defined in `pkg/circuits/types.go` with parameters, examples, and documentation
- Embedded firmware data from official DROID Forge loaded at runtime
- Search functionality by name, category, or description
- Validation of circuit parameters and register usage

### Configuration Validation
- INI-style configuration parsing in `pkg/droid/parser.go`
- Multi-layer validation: syntax, semantics, circuit parameters, register usage
- Cable connection validation for DROID-specific wiring rules

### Device Management
- Core MIDI integration for device discovery and communication
- Active vs offline device detection
- Device filtering and pattern matching
- JSON output support for scripting integration

## Recent Optimizations & Improvements

### C Library Optimizations (2024)
Major performance and maintainability improvements to the C library:

#### Device Enumeration Consolidation
- **Before**: 145+ lines of duplicate code across device listing functions
- **After**: Single `enumerate_devices_internal()` function with helper functions
- **Benefits**: ~120 lines reduced, consistent logic, eliminated redundant MIDI API calls

#### SysEx Buffer Management
- **Optimization**: Reduced from 3 to 2 buffer allocations using `buffer_resize()`
- **Buffer Growth**: Implemented 50% growth strategy to reduce reallocation frequency
- **Memory Impact**: ~33% fewer allocations for typical operations

#### Enhanced Error Handling
- **Input Validation**: Centralized helpers (`validate_filename()`, `validate_buffer_params()`)
- **Consistent Messages**: Better user experience with descriptive error messages
- **Path Validation**: Added length limits and improved safety checks

#### API Improvements
- **Documentation**: Enhanced function documentation with parameter requirements
- **Resource Management**: Better cleanup patterns and NULL-safety
- **Performance**: Optimized for both memory usage and execution speed

### Structured Logging System (2024)
Replaced ad-hoc `fmt.Printf` statements with professional structured logging:

#### Logging Infrastructure
- **Library**: Uses `github.com/sirupsen/logrus` for structured logging
- **Package**: Custom `pkg/logger/` with clean API and custom formatters
- **Levels**: Full support for trace, debug, info, warn, error, fatal, panic

#### Command Line Control
- **New Flag**: `--log-level` with all severity options
- **Backward Compatibility**: `--verbose` maps to debug level, `--quiet` still works
- **Examples**:
  ```bash
  droid --log-level debug device list    # Detailed diagnostic output
  droid --log-level warn upload config   # Only warnings and errors
  droid --verbose validate config        # Same as --log-level debug
  ```

#### Intelligent Message Classification
- **Debug**: Internal operations, API calls, diagnostic information
- **Info**: User-facing status updates, successful operations, progress indicators
- **Warn**: Non-critical issues (like `--force` usage, deprecated features)
- **Error**: Validation failures, upload errors, critical issues

#### Structured Context Fields
Messages include relevant context for better debugging:
```bash
# Debug level shows full context
DEBUG Device listing completed active_only=false pattern="" total_devices=1

# Info level shows clean user messages
INFO Starting configuration validation file=test.ini strict=false format=text
```

#### Output Formatting
- **Debug/Trace**: Full timestamps, colored output, detailed structured fields
- **Info and above**: Clean user-friendly messages without noise
- **Error/Warn**: Clear level indicators for important messages
- **Quiet Mode**: Respects existing `--quiet` behavior

### Real-Time Device Connectivity Detection (2024)
Implemented robust device connectivity detection for upload watch mode, solving Core MIDI caching issues:

#### The Problem
- **Core MIDI Aggressive Caching**: When MIDI devices are disconnected, Core MIDI continues to report them as available for several seconds
- **False Positive Uploads**: System would report successful uploads even when device was physically disconnected
- **Poor User Experience**: No feedback when device was disconnected during watch mode

#### The Solution - `kMIDIPropertyOffline` Discovery
The breakthrough was discovering that `kMIDIPropertyOffline` provides immediate and reliable device status:

```c
// Key discovery: This property detects disconnection immediately
SInt32 offline = 0;
OSStatus status = MIDIObjectGetIntegerProperty(endpoint, kMIDIPropertyOffline, &offline);
if (status == noErr && offline != 0) {
    return DROID_ERROR_MIDI_DEVICE_NOT_FOUND; // Device is offline
}
```

#### Implementation Details
- **`midi_validate_endpoint()`**: Simplified to use only `kMIDIPropertyOffline` check
- **`is_device_online()`**: Simplified to use only `kMIDIPropertyOffline` check
- **Watch Mode**: Periodic device checking every 5 seconds with state change detection
- **User Feedback**: Clear status messages for device disconnection/reconnection
- **Automatic Resume**: Uploads automatically resume when device reconnects

#### Key Technical Insights
- **`kMIDIPropertyOffline`** is sufficient for reliable device disconnection detection (immediate detection)
- **Simplified approach** eliminates complex endpoint enumeration while maintaining accuracy
- **Property-based validation** is much more efficient than communication tests or endpoint iteration
- **State tracking** provides seamless user experience with clear status updates
- **Code reduction** of ~30 lines by removing redundant fallback validation logic

#### User Experience Improvements
```bash
# Device connected - normal operation
INFO Target device is connected device="DROID X7 MIDI"
INFO Configuration uploaded successfully

# Device disconnected - immediate detection
WARNING Device disconnected device="DROID X7 MIDI"
WARNING Device not connected - skipping upload (file will be uploaded when device reconnects)

# Device reconnected - automatic resume (within 5 seconds)
INFO Device reconnected - ready for uploads device="DROID X7 MIDI"
INFO Configuration uploaded successfully
```

#### Files Modified
- `lib/midi.c`: Simplified `midi_validate_endpoint()` to use only `kMIDIPropertyOffline` check
- `lib/libdroid.c`: Simplified `is_device_online()` to use only `kMIDIPropertyOffline` check
- `lib/libdroid.c/h`: Added `droid_is_device_connected()` and `droid_refresh_device_cache()`
- `pkg/droid/droid.go`: Added Go bindings for device connectivity functions
- `cmd/droid/commands/upload.go`: Integrated connectivity detection into watch mode with state tracking

This implementation now provides device connectivity detection that rivals professional MIDI software, with immediate disconnection detection and automatic reconnection handling, using a simplified and more maintainable approach.

### Development Best Practices
- **Testing**: All optimizations maintain full test coverage
- **Compatibility**: Backward compatible with existing CLI usage patterns
- **Performance**: Measurable improvements in memory usage and execution speed
- **Maintainability**: Cleaner code structure with consistent patterns
- **Hardware Testing**: Always prompt to connect/disconnect DROID X7 MIDI during device connectivity testing
