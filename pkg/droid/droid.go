package droid

/*
#cgo LDFLAGS: -L../../lib -ldroid -framework CoreMIDI -framework CoreFoundation
#include "../../lib/libdroid.h"
#include <stdlib.h>
*/
import "C"
import (
	"errors"
	"fmt"
	"os"
	"unsafe"
)

// ErrorCode represents a DROID library error code
type ErrorCode int

const (
	Success ErrorCode = iota
	ErrorFileNotFound
	ErrorFileReadFailed
	ErrorDeviceNotFound
	ErrorMIDIFailed
	ErrorInvalidConfig
	ErrorMemoryAllocation
	ErrorInvalidArgs
	ErrorSysExEncoding
)

// Error represents a DROID operation error
type Error struct {
	Code    ErrorCode
	Message string
}

func (e *Error) Error() string {
	return fmt.Sprintf("DROID error %d: %s", e.Code, e.Message)
}

// Device represents a MIDI device
type Device struct {
	Name string
	ID   int
}

// MIDIDevice represents a MIDI device with additional information
type MIDIDevice struct {
	Name     string `json:"name"`
	ID       int    `json:"id"`
	IsInput  bool   `json:"is_input"`
	IsOutput bool   `json:"is_output"`
	Type     string `json:"type,omitempty"`
	IsActive bool   `json:"is_active"`
	LastSeen string `json:"last_seen,omitempty"`
}

// UploadOptions contains options for uploading configurations
type UploadOptions struct {
	DeviceName string
	Port       int
	DryRun     bool
	Verify     bool
	Verbose    bool
	Quiet      bool
}

// ValidationOptions contains options for validation
type ValidationOptions struct {
	Strict  bool
	Verbose bool
}

// ValidationResult contains the result of validation
type ValidationResult struct {
	IsValid        bool     `json:"is_valid"`
	Errors         []string `json:"errors,omitempty"`
	Warnings       []string `json:"warnings,omitempty"`
	CircuitCount   int      `json:"circuit_count"`
	ParameterCount int      `json:"parameter_count"`
}

// ConfigInitOptions contains options for initializing configs
type ConfigInitOptions struct {
	Template  string
	Overwrite bool
	Verbose   bool
}

// FormatOptions contains options for formatting configs
type FormatOptions struct {
	CreateBackup bool
	Verbose      bool
}

// DeviceListOptions contains options for listing devices
type DeviceListOptions struct {
	ActiveOnly bool
	ShowAll    bool
}

// convertError converts a C result to a Go error
func convertError(result *C.DroidLibResult) error {
	if result.error_code == C.DROID_LIB_SUCCESS {
		return nil
	}

	var code ErrorCode
	switch result.error_code {
	case C.DROID_LIB_ERROR_FILE_NOT_FOUND:
		code = ErrorFileNotFound
	case C.DROID_LIB_ERROR_FILE_READ_FAILED:
		code = ErrorFileReadFailed
	case C.DROID_LIB_ERROR_DEVICE_NOT_FOUND:
		code = ErrorDeviceNotFound
	case C.DROID_LIB_ERROR_MIDI_FAILED:
		code = ErrorMIDIFailed
	case C.DROID_LIB_ERROR_INVALID_CONFIG:
		code = ErrorInvalidConfig
	case C.DROID_LIB_ERROR_MEMORY_ALLOCATION:
		code = ErrorMemoryAllocation
	case C.DROID_LIB_ERROR_INVALID_ARGS:
		code = ErrorInvalidArgs
	case C.DROID_LIB_ERROR_SYSEX_ENCODING:
		code = ErrorSysExEncoding
	default:
		code = ErrorMIDIFailed
	}

	message := "Unknown error"
	if result.error_message != nil {
		message = C.GoString(result.error_message)
	}

	return &Error{Code: code, Message: message}
}

// UploadFile uploads a DROID configuration file to a device
func UploadFile(filename, deviceName string, verbose, dryRun bool) error {
	return UploadFileWithOptions(filename, UploadOptions{
		DeviceName: deviceName,
		Verbose:    verbose,
		DryRun:     dryRun,
	})
}

// UploadBuffer uploads DROID configuration data from a buffer to a device
func UploadBuffer(data []byte, deviceName string, verbose, dryRun bool) error {
	var cData *C.uint8_t
	if len(data) > 0 {
		cData = (*C.uint8_t)(unsafe.Pointer(&data[0]))
	}

	var cDeviceName *C.char
	if deviceName != "" {
		cDeviceName = C.CString(deviceName)
		defer C.free(unsafe.Pointer(cDeviceName))
	}

	cVerbose := C.int(0)
	if verbose {
		cVerbose = C.int(1)
	}

	cDryRun := C.int(0)
	if dryRun {
		cDryRun = C.int(1)
	}

	result := C.droid_upload_buffer(cData, C.size_t(len(data)), cDeviceName, cVerbose, cDryRun)
	defer C.droid_free_result(result)

	return convertError(result)
}

// ListDevices returns a list of available MIDI devices (active only by default)
func ListDevices() ([]MIDIDevice, error) {
	return ListDevicesWithOptions(DeviceListOptions{
		ActiveOnly: false,
		ShowAll:    false, // Default: show active only
	})
}

// ListDevicesWithOptions returns a list of MIDI devices with options
func ListDevicesWithOptions(opts DeviceListOptions) ([]MIDIDevice, error) {
	// Use the new C function that supports filtering
	activeOnly := 0
	if opts.ActiveOnly || !opts.ShowAll {
		// Default behavior: show only active devices unless --all is specified
		activeOnly = 1
	}

	deviceList := C.droid_list_devices_with_options(C.int(activeOnly))
	defer C.droid_free_device_list(deviceList)

	if deviceList.error_code != C.DROID_LIB_SUCCESS {
		message := "Unknown error"
		if deviceList.error_message != nil {
			message = C.GoString(deviceList.error_message)
		}
		return nil, errors.New(message)
	}

	devices := make([]MIDIDevice, int(deviceList.device_count))
	for i := 0; i < int(deviceList.device_count); i++ {
		// Access the device at index i
		device := (*C.DroidDevice)(unsafe.Pointer(uintptr(unsafe.Pointer(deviceList.devices)) +
			uintptr(i)*unsafe.Sizeof(*deviceList.devices)))

		devices[i] = MIDIDevice{
			Name:     C.GoString(device.name),
			ID:       int(device.id),
			IsInput:  true, // Assume both for now
			IsOutput: true, // Assume both for now
			Type:     "DROID",
			IsActive: device.is_active == 1,
			LastSeen: "now",
		}
	}

	return devices, nil
}

// ValidateFile validates a DROID configuration file
func ValidateFile(filename string) error {
	cFilename := C.CString(filename)
	defer C.free(unsafe.Pointer(cFilename))

	result := C.droid_validate_file(cFilename)
	defer C.droid_free_result(result)

	return convertError(result)
}

// ValidateBuffer validates DROID configuration data from a buffer
func ValidateBuffer(data []byte) error {
	var cData *C.uint8_t
	if len(data) > 0 {
		cData = (*C.uint8_t)(unsafe.Pointer(&data[0]))
	}

	result := C.droid_validate_buffer(cData, C.size_t(len(data)))
	defer C.droid_free_result(result)

	return convertError(result)
}

// RefreshDeviceCache forces a refresh of the MIDI device cache
func RefreshDeviceCache() {
	C.droid_refresh_device_cache()
}

// IsDeviceConnected checks if a specific DROID device is currently connected
func IsDeviceConnected(deviceName string) bool {
	var cDeviceName *C.char
	if deviceName != "" {
		cDeviceName = C.CString(deviceName)
		defer C.free(unsafe.Pointer(cDeviceName))
	}

	result := C.droid_is_device_connected(cDeviceName)
	return result == 1
}

// GetVersion returns the library version
func GetVersion() string {
	return C.GoString(C.droid_get_version())
}

// UploadFileWithOptions uploads a file with the given options
func UploadFileWithOptions(filename string, opts UploadOptions) error {
	cFilename := C.CString(filename)
	defer C.free(unsafe.Pointer(cFilename))

	var cDeviceName *C.char
	if opts.DeviceName != "" {
		cDeviceName = C.CString(opts.DeviceName)
		defer C.free(unsafe.Pointer(cDeviceName))
	}

	cVerbose := C.int(0)
	if opts.Verbose {
		cVerbose = C.int(1)
	}

	cDryRun := C.int(0)
	if opts.DryRun {
		cDryRun = C.int(1)
	}

	result := C.droid_upload_file(cFilename, cDeviceName, cVerbose, cDryRun)
	defer C.droid_free_result(result)

	return convertError(result)
}

// ValidateFileWithOptions validates a file with the given options
func ValidateFileWithOptions(filename string, opts ValidationOptions) (*ValidationResult, error) {
	// Read the file
	content, err := readFileContent(filename)
	if err != nil {
		return &ValidationResult{
			IsValid: false,
			Errors:  []string{err.Error()},
		}, nil
	}

	// Parse the configuration
	config, parseErrors := ParseConfig(string(content))

	// Validate circuit parameters
	validationErrors := ValidateCircuitParameters(config)

	// Validate register usage (temporarily disabled to focus on required inputs)
	// registerErrors := ValidateRegisterUsage(config)
	var registerErrors []*DroidError

	// Validate critical safety rules
	circuitCountErrors := ValidateCircuitCountLimits(config)
	registerRangeErrors := ValidateRegisterNumberRanges(config)

	// Validate required inputs
	requiredInputErrors := ValidateRequiredInputs(config)

	// Validate output destinations
	outputDestinationErrors := ValidateOutputDestinations(config)

	// Validate cable connections
	cableConnectionErrors := ValidateCableConnections(config)

	// Combine all errors
	var allErrors []string
	for _, err := range parseErrors {
		allErrors = append(allErrors, err.Error())
	}
	for _, err := range validationErrors {
		allErrors = append(allErrors, err.Error())
	}
	for _, err := range registerErrors {
		allErrors = append(allErrors, err.Error())
	}
	for _, err := range circuitCountErrors {
		allErrors = append(allErrors, err.Error())
	}
	for _, err := range registerRangeErrors {
		allErrors = append(allErrors, err.Error())
	}
	for _, err := range requiredInputErrors {
		allErrors = append(allErrors, err.Error())
	}
	for _, err := range outputDestinationErrors {
		allErrors = append(allErrors, err.Error())
	}
	for _, err := range cableConnectionErrors {
		allErrors = append(allErrors, err.Error())
	}

	result := &ValidationResult{
		IsValid:        len(allErrors) == 0,
		CircuitCount:   len(config.Circuits),
		ParameterCount: countParameters(config),
		Errors:         allErrors,
	}

	return result, nil
}

// readFileContent reads file content as bytes
func readFileContent(filename string) ([]byte, error) {
	// Use Go's standard library for file reading
	content, err := os.ReadFile(filename)
	if err != nil {
		return nil, fmt.Errorf("failed to read file: %w", err)
	}
	return content, nil
}

// countParameters counts total parameters across all circuits
func countParameters(config *DroidConfig) int {
	count := 0
	for _, circuit := range config.Circuits {
		count += len(circuit.Parameters)
	}
	return count
}

// InitConfigFile initializes a new config file
func InitConfigFile(filename string, opts ConfigInitOptions) error {
	// TODO: implement config file initialization
	return fmt.Errorf("config initialization not yet implemented")
}

// FormatConfigFile formats a config file
func FormatConfigFile(filename string, opts FormatOptions) error {
	// TODO: implement config file formatting
	return fmt.Errorf("config formatting not yet implemented")
}

// GetConfigTemplate returns a config template
func GetConfigTemplate(templateName string) (string, error) {
	templates := map[string]string{
		"basic": `# Basic DROID Configuration
# Generated by DROID Tools

[droid]
# Add your circuits here

# Example LFO
[lfo]
hz = 1
sine = O1

# Example button
[button]
button = B1.1
led = L1.1
`,
		"drums": `# Drum Machine Configuration
# Generated by DROID Tools

[droid]

# Clock generator
[lfo]
hz = 2
square = _CLOCK

# Drum sequencer
[algoquencer]
clock = _CLOCK
pattern = 1
gate1 = O1  # Kick
gate2 = O2  # Snare
gate3 = O3  # Hi-hat
`,
		"sequencer": `# Sequencer Configuration
# Generated by DROID Tools

[droid]

# Master clock
[lfo]
hz = 2
square = _MASTER_CLOCK

# Main sequencer
[sequencer]
clock = _MASTER_CLOCK
pitch1 = O1
gate1 = O2
`,
		"midi": `# MIDI Configuration
# Generated by DROID Tools

[droid]

# MIDI input
[midiin]
channel = 1
pitch = _MIDI_PITCH
gate = _MIDI_GATE
velocity = _MIDI_VEL

# MIDI output
[midiout]
pitch = I1
gate = I2
velocity = 0.8
channel = 1
`,
	}

	template, exists := templates[templateName]
	if !exists {
		return "", fmt.Errorf("unknown template: %s", templateName)
	}

	return template, nil
}
