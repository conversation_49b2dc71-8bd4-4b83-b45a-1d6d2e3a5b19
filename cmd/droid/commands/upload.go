package commands

import (
	"fmt"
	"os"
	"os/signal"
	"path/filepath"
	"syscall"
	"time"

	"github.com/fsnotify/fsnotify"
	"github.com/spf13/cobra"
	"github.com/t6d/droid-tools/pkg/droid"
	"github.com/t6d/droid-tools/pkg/logger"
)

var (
	deviceName string
	dryRun     bool
	port       int
	force      bool
	watch      bool
)

// uploadCmd represents the upload command
var uploadCmd = &cobra.Command{
	Use:   "upload [flags] <config-file>",
	Short: "Upload configuration file to DROID device",
	Long: `Upload a DROID configuration file to a connected DROID device via MIDI.

The upload command automatically validates the configuration file before uploading.
If validation fails, the upload is aborted unless --force is specified.

The upload command automatically detects connected DROID devices and uploads
the specified configuration file. You can specify a particular device or
MIDI port if multiple devices are connected.

Examples:
  droid upload my-config.ini
  droid upload --device "DROID Master" configs/drums.ini
  droid upload --dry-run test-config.ini
  droid upload --force invalid-config.ini  # Skip validation
  droid upload --watch my-config.ini       # Watch for changes`,
	Args: cobra.ExactArgs(1),
	RunE: func(cmd *cobra.Command, args []string) error {
		filename := args[0]

		logFields := logger.WithFields(map[string]interface{}{
			"file": filename,
			"mode": func() string {
				if watch {
					return "watch"
				}
				return "single"
			}(),
		})

		if deviceName != "" {
			logFields = logFields.WithField("device", deviceName)
		}
		if port > 0 {
			logFields = logFields.WithField("port", port)
		}
		if dryRun {
			logFields = logFields.WithField("dry_run", true)
		}
		if force {
			logFields = logFields.WithField("force", true)
		}

		if watch {
			logFields.Info("Starting upload in watch mode")
		} else {
			logFields.Info("Starting configuration upload")
		}

		// Create upload options
		opts := droid.UploadOptions{
			DeviceName: deviceName,
			Port:       port,
			DryRun:     dryRun,
			Verbose:    IsVerbose(),
			Quiet:      IsQuiet(),
		}

		// Choose between watch mode and single upload
		if watch {
			return watchAndUpload(filename, opts, force)
		} else {
			return performUpload(filename, opts, force)
		}
	},
}

// performUpload executes the upload with validation (shared by single upload and watch mode)
func performUpload(filename string, opts droid.UploadOptions, force bool) error {
	// Check device connectivity before proceeding (only for actual uploads, not dry runs)
	if !opts.DryRun {
		targetDevice := opts.DeviceName
		if targetDevice == "" {
			targetDevice = "DROID X7 MIDI"
		}

		if !droid.IsDeviceConnected(targetDevice) {
			return fmt.Errorf("DROID device '%s' not found. Please check device connection", targetDevice)
		}

		logger.WithField("device", targetDevice).Debug("Device connectivity confirmed")
	}

	// Validate configuration before upload (unless --force is specified)
	if !force {
		logger.Debug("Validating configuration before upload")

		validationOpts := droid.ValidationOptions{
			Strict:  false, // Use normal validation for upload
			Verbose: IsVerbose(),
		}

		result, err := droid.ValidateFileWithOptions(filename, validationOpts)
		if err != nil {
			return fmt.Errorf("validation failed: %v", err)
		}

		if !result.IsValid {
			logger.Error("Configuration file has validation errors:")
			for _, errMsg := range result.Errors {
				logger.Errorf("  - %s", errMsg)
			}
			if !watch {
				logger.Error("Upload aborted. Use --force to upload anyway (not recommended).")
			} else {
				logger.Error("Skipping upload. Use --force to upload anyway (not recommended).")
			}
			return fmt.Errorf("configuration validation failed")
		}

		logger.WithFields(map[string]interface{}{
			"circuits":   result.CircuitCount,
			"parameters": result.ParameterCount,
		}).Info("Configuration validated successfully")
	} else {
		logger.Warn("Skipping validation (--force specified)")
	}

	// Perform the upload
	err := droid.UploadFileWithOptions(filename, opts)
	if err != nil {
		return fmt.Errorf("upload failed: %v", err)
	}

	if opts.DryRun {
		logger.Info("Configuration validated successfully (dry run)")
	} else {
		logger.Info("Configuration uploaded successfully")
	}

	return nil
}

// watchAndUpload watches a file for changes and uploads on modification
func watchAndUpload(filename string, opts droid.UploadOptions, force bool) error {
	// Get absolute path for reliable watching
	absPath, err := filepath.Abs(filename)
	if err != nil {
		return fmt.Errorf("failed to get absolute path: %v", err)
	}

	// Create file watcher
	watcher, err := fsnotify.NewWatcher()
	if err != nil {
		return fmt.Errorf("failed to create file watcher: %v", err)
	}
	defer watcher.Close()

	// Add file to watcher
	err = watcher.Add(absPath)
	if err != nil {
		return fmt.Errorf("failed to watch file %s: %v", absPath, err)
	}

	// Set up signal handling for graceful shutdown
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	logger.WithField("file", filename).Info("Starting file watcher (Press Ctrl+C to stop)")

	// Check initial device connectivity
	targetDevice := opts.DeviceName
	if targetDevice == "" {
		targetDevice = "DROID X7 MIDI"
	}

	if !droid.IsDeviceConnected(targetDevice) {
		logger.WithField("device", targetDevice).Warn("Target device not currently connected - will retry when file changes")
	} else {
		logger.WithField("device", targetDevice).Info("Target device is connected")
	}

	// Perform initial upload
	if err := performUpload(filename, opts, force); err != nil {
		logger.WithError(err).Error("Initial upload failed")
	}

	// Debouncing variables
	var lastUpload time.Time
	const debounceDelay = 2 * time.Second

	// Device connectivity state tracking
	var lastDeviceCheck time.Time
	var lastKnownConnected = droid.IsDeviceConnected(targetDevice)
	const deviceCheckInterval = 5 * time.Second // Check device status every 5 seconds

	// Watch loop
	for {
		select {
		case event, ok := <-watcher.Events:
			if !ok {
				return nil
			}

			// Only process write events
			if event.Op&fsnotify.Write == fsnotify.Write {
				// Debounce rapid file changes
				now := time.Now()
				if now.Sub(lastUpload) < debounceDelay {
					continue
				}
				lastUpload = now

				// Check device connectivity before uploading
				isConnected := droid.IsDeviceConnected(targetDevice)
				logger.WithFields(map[string]interface{}{
					"device":    targetDevice,
					"connected": isConnected,
				}).Debug("Device connectivity check")

				// Update device state tracking
				if isConnected != lastKnownConnected {
					if isConnected {
						logger.WithField("device", targetDevice).Info("Device reconnected - resuming uploads")
					} else {
						logger.WithField("device", targetDevice).Warn("Device disconnected")
					}
					lastKnownConnected = isConnected
				}

				if !isConnected {
					logger.WithField("device", targetDevice).Warn("Device not connected - skipping upload (file will be uploaded when device reconnects)")
					continue
				}

				logger.Info("File changed, uploading...")
				if err := performUpload(filename, opts, force); err != nil {
					logger.WithError(err).Error("Upload failed")
				} else {
					// Give DROID time to process the upload before next file change
					// This prevents rapid successive uploads from interfering with each other
					time.Sleep(1 * time.Second)
				}
			}

		case err, ok := <-watcher.Errors:
			if !ok {
				return nil
			}
			logger.WithError(err).Error("File watcher error")

		case <-sigChan:
			logger.Info("Stopping file watcher...")
			return nil

		case <-time.After(deviceCheckInterval):
			// Periodic device connectivity check
			if time.Since(lastDeviceCheck) >= deviceCheckInterval {
				// Direct connectivity check using kMIDIPropertyOffline
				// This is reliable and immediate - no fallback needed
				isConnected := droid.IsDeviceConnected(targetDevice)

				logger.WithFields(map[string]interface{}{
					"device":    targetDevice,
					"connected": isConnected,
				}).Trace("Periodic device connectivity check")

				// Update device state tracking
				if isConnected != lastKnownConnected {
					if isConnected {
						logger.WithField("device", targetDevice).Info("Device reconnected - ready for uploads")
					} else {
						logger.WithField("device", targetDevice).Debug("Device still disconnected")
					}
					lastKnownConnected = isConnected
				}
				lastDeviceCheck = time.Now()
			}
		}
	}
}

func init() {
	uploadCmd.Flags().StringVarP(&deviceName, "device", "d", "", "target DROID device name")
	uploadCmd.Flags().IntVarP(&port, "port", "p", 0, "MIDI port number (0 = auto-detect)")
	uploadCmd.Flags().BoolVar(&dryRun, "dry-run", false, "show what would be uploaded without uploading")
	uploadCmd.Flags().BoolVar(&force, "force", false, "skip validation and force upload (use with caution)")
	uploadCmd.Flags().BoolVar(&watch, "watch", false, "watch file for changes and auto-upload")
}
